-- 添加新的 dify_chatflow_blocking 模型类型到 ai_agent_profiles 表
-- 这个脚本用于支持新的堵塞模式 Dify ChatFlow 模型

-- 修改 model_type 枚举类型，添加 'dify_chatflow_blocking'
ALTER TABLE `ai_agent_profiles` 
MODIFY COLUMN `model_type` enum(
    'gpt',
    'qwen',
    'dify',
    'wenxin',
    'dify_chatflow',
    'dify_chatflow_blocking',
    'other',
    'dify_agent',
    'dify_workflow',
    'gpt-3.5-turbo'
) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'other';

-- 验证修改是否成功
DESCRIBE ai_agent_profiles;

-- 显示当前所有智能体配置
SELECT id, name, model_type, description FROM ai_agent_profiles ORDER BY id;
