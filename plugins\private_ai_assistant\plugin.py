"""
私聊AI助手插件
专门处理私聊消息，利用上下文历史记录提供智能回复
默认使用智能体4，无需关键词触发
"""

import json
from typing import List, Optional, Dict
from datetime import datetime

from core.plugin_base import Plugin
from models import (
    AIModel,
    GPT35TurboModel,
    DifyChatFlowModel,
    DifyChatFlowBlockingModel,
    DifyWorkFlowModel,
    DifyAgentModel
)
from .sql import PRIVATE_AI_ASSISTANT_SQL_FUNCTIONS
from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS


class PrivateAIAssistantPlugin(Plugin):
    """私聊AI助手插件"""
    
    MODEL_MAP = {
        "gpt-3.5-turbo": GPT35TurboModel,
        "dify_chatflow": DifyChatFlowModel,
        "dify_chatflow_blocking": DifyChatFlowBlockingModel,
        "dify_workflow": DifyWorkFlowModel,
        "dify_agent": DifyAgentModel
    }
    
    priority = 150  # 中等优先级，由消息路由插件调用
    
    def __init__(self, handler):
        super().__init__(handler)
        
        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")
        
        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("PrivateAIAssistantPlugin", PRIVATE_AI_ASSISTANT_SQL_FUNCTIONS)
        
        # 注册对话历史记录功能
        self.db.register_plugin_functions("PrivateAIAssistant_History", CONVERSATION_HISTORY_SQL_FUNCTIONS)
        
        # 初始化数据库表
        try:
            self.db.init_private_ai_tables()
            self.db.init_conversation_tables()
            self.handler._log("✅ [私聊AI助手] 数据库表初始化完成")
        except Exception as e:
            self.handler._log(f"❌ [私聊AI助手] 初始化数据库表失败: {e}", level="ERROR")
        
        # 配置缓存
        self.config_cache = {}
        self.last_cache_update = {}
        
        # 默认配置
        self.default_config = {
            'enabled': True,
            'ai_agent_id': 4,  # 默认使用智能体4
            'context_limit': 20,
            'session_hours': 24,
            'personality': json.dumps({
                'name': 'AI助手',
                'style': 'friendly',
                'features': ['helpful', 'conversational', 'context_aware']
            })
        }
        
        self.handler._log("✅ [私聊AI助手] 插件初始化完成")
    
    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("PrivateAIAssistantPlugin")
            self.db.unregister_plugin_functions("PrivateAIAssistant_History")
    
    def _get_user_config(self, user_name: str) -> Dict:
        """获取用户配置，如果不存在则创建默认配置"""
        # 检查缓存
        cache_key = user_name
        now = datetime.now()
        
        if (cache_key in self.config_cache and 
            cache_key in self.last_cache_update and
            (now - self.last_cache_update[cache_key]).seconds < 300):  # 5分钟缓存
            return self.config_cache[cache_key]
        
        # 从数据库获取配置
        config = self.db.get_private_ai_config(user_name)
        
        if not config:
            # 创建默认配置
            self.db.upsert_private_ai_config(
                user_name=user_name,
                **self.default_config
            )
            config = self.default_config.copy()
            config['user_name'] = user_name
            self.handler._log(f"[私聊AI助手] 为用户 {user_name} 创建默认配置")
        
        # 更新缓存
        self.config_cache[cache_key] = config
        self.last_cache_update[cache_key] = now
        
        return config
    
    def _is_private_chat(self, chat: str) -> bool:
        """判断是否为私聊"""
        # 从handler获取聊天类型缓存
        chat_type_cache = getattr(self.handler, 'chat_type_cache', {})
        chat_type = chat_type_cache.get(chat, 'friend')  # 默认为私聊
        return chat_type == 'friend'
    
    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        处理私聊消息
        只处理私聊，无需关键词触发，用户发什么就回复什么
        """
        # 只处理私聊消息
        if not self._is_private_chat(chat):
            return None
        
        if not messages:
            return None
        
        # 获取用户配置
        user_config = self._get_user_config(chat)
        
        # 检查是否启用
        if not user_config.get('enabled', True):
            return None
        
        try:
            # 更新会话统计
            self.db.update_session_stats(chat, 'user')
            
            # 保存用户消息到历史记录
            user_message_info = self._save_user_messages_to_history(chat, messages, user_config)
            
            # 获取AI回复
            ai_response = self._generate_ai_reply(chat, messages, user_config)
            
            if ai_response:
                # 保存AI回复到历史记录
                self._save_ai_reply_to_history(chat, ai_response, user_message_info, user_config)
                
                # 更新AI回复统计
                self.db.update_session_stats(chat, 'ai')
                
                self.handler._log(f"[私聊AI助手] 为用户 {chat} 生成回复: {ai_response[:50]}...")
                return ai_response
            
        except Exception as e:
            self.handler._log(f"[私聊AI助手] 处理用户 {chat} 消息失败: {e}", level="ERROR")

        return None

    def _save_user_messages_to_history(self, chat: str, messages: List[str], user_config: Dict) -> Dict:
        """保存用户消息到历史记录"""
        try:
            # 生成对话ID和批次ID
            conversation_id = self.db.generate_conversation_id()
            message_batch_id = self.db.generate_batch_id() if len(messages) > 1 else None

            # 合并所有用户消息
            combined_content = " ".join(messages)

            # 保存用户消息
            user_message_id = self.db.save_user_message_with_batch(
                chat_name=chat,
                chat_type='private',
                sender=chat,  # 私聊中用户名就是聊天名
                message_content=combined_content,
                session_hours=user_config.get('session_hours', 24),
                conversation_id=conversation_id,
                message_batch_id=message_batch_id,
                is_batch_start=len(messages) > 1,
                is_batch_end=len(messages) > 1
            )

            self.handler._log(f"[私聊AI助手] 保存用户消息到历史记录 (ID: {user_message_id})")

            return {
                'user_message_id': user_message_id,
                'conversation_id': conversation_id,
                'message_batch_id': message_batch_id,
                'combined_content': combined_content
            }

        except Exception as e:
            self.handler._log(f"[私聊AI助手] 保存用户消息失败: {e}", level="ERROR")
            return {}

    def _generate_ai_reply(self, chat: str, messages: List[str], user_config: Dict) -> Optional[str]:
        """生成AI回复"""
        try:
            # 获取AI智能体配置
            agent_id = user_config.get('ai_agent_id', 4)
            agent_config = self.db.get_ai_agent_by_id(agent_id)

            if not agent_config:
                self.handler._log(f"[私聊AI助手] 未找到AI智能体配置 ID: {agent_id}")
                return None

            # 获取历史上下文
            context_limit = user_config.get('context_limit', 20)
            history_context = self._get_conversation_context(chat, context_limit)

            # 构建提示词
            prompt = self._build_context_prompt(history_context, messages, user_config)

            # 调用AI模型
            model_type = agent_config.get("model_type", "gpt-3.5-turbo")
            model_class = self.MODEL_MAP.get(model_type)

            if not model_class:
                self.handler._log(f"[私聊AI助手] 不支持的模型类型: {model_type}")
                return None

            # 实例化模型
            model_kwargs = {
                "api_key": agent_config["api_key"],
                "url": agent_config["url"]
            }

            ai_model: AIModel = model_class(**model_kwargs)
            ai_response = ai_model.generate(context=prompt)

            return ai_response

        except Exception as e:
            self.handler._log(f"[私聊AI助手] 生成AI回复失败: {e}", level="ERROR")
            return None

    def _get_conversation_context(self, chat: str, limit: int) -> List[Dict]:
        """获取对话上下文"""
        try:
            return self.db.get_conversation_context(chat, 'private', limit)
        except Exception as e:
            self.handler._log(f"[私聊AI助手] 获取对话上下文失败: {e}", level="ERROR")
            return []

    def _build_context_prompt(self, history_context: List[Dict], current_messages: List[str],
                            user_config: Dict) -> str:
        """构建包含上下文的提示词"""
        prompt_lines = []

        # 添加个性化设置
        personality = user_config.get('personality')
        if personality:
            try:
                personality_data = json.loads(personality) if isinstance(personality, str) else personality
                name = personality_data.get('name', 'AI助手')
                style = personality_data.get('style', 'friendly')
                features = personality_data.get('features', [])

                prompt_lines.append(f"你是{name}，以{style}的风格与用户对话。")
                if features:
                    prompt_lines.append(f"你的特点包括：{', '.join(features)}。")
            except:
                prompt_lines.append("你是一个友好的AI助手。")
        else:
            prompt_lines.append("你是一个友好的AI助手。")

        # 添加历史对话上下文
        if history_context:
            prompt_lines.append("\n=== 历史对话上下文 ===")
            for msg in history_context:
                role = "用户" if msg['message_type'] == 'user' else "助手"
                prompt_lines.append(f"{role}: {msg['message_content']}")
            prompt_lines.append("=== 当前对话 ===")

        # 添加当前用户消息
        for msg in current_messages:
            prompt_lines.append(f"用户: {msg}")

        prompt_lines.append("\n请基于以上对话历史，给出自然、有帮助的回复：")

        return "\n".join(prompt_lines)

    def _save_ai_reply_to_history(self, chat: str, ai_response: str, user_message_info: Dict,
                                 user_config: Dict):
        """保存AI回复到历史记录"""
        if not user_message_info:
            self.handler._log("[私聊AI助手] 用户消息信息为空，无法保存AI回复关联", level="WARNING")
            return

        try:
            # 保存AI回复并建立关联
            reply_id = self.db.save_bot_reply(
                chat_name=chat,
                chat_type='private',
                message_content=ai_response,
                session_hours=user_config.get('session_hours', 24),
                conversation_id=user_message_info.get('conversation_id'),
                reply_to_id=user_message_info.get('user_message_id'),
                message_batch_id=user_message_info.get('message_batch_id')
            )

            self.handler._log(f"[私聊AI助手] 保存AI回复到历史记录 (回复ID: {reply_id}, 关联消息ID: {user_message_info.get('user_message_id')})")

        except Exception as e:
            self.handler._log(f"[私聊AI助手] 保存AI回复失败: {e}", level="ERROR")

    def get_user_conversation_history(self, user_name: str, limit: int = 20) -> List[Dict]:
        """获取用户的对话历史"""
        try:
            return self.db.get_conversation_context(user_name, 'private', limit)
        except Exception as e:
            self.handler._log(f"[私聊AI助手] 获取用户对话历史失败: {e}", level="ERROR")
            return []

    def get_user_session_stats(self, user_name: str, days: int = 7) -> List[Dict]:
        """获取用户会话统计"""
        try:
            return self.db.get_user_session_stats(user_name, days)
        except Exception as e:
            self.handler._log(f"[私聊AI助手] 获取用户会话统计失败: {e}", level="ERROR")
            return []

    def update_user_config(self, user_name: str, **config_updates):
        """更新用户配置"""
        try:
            current_config = self._get_user_config(user_name)

            # 合并配置更新
            updated_config = current_config.copy()
            updated_config.update(config_updates)

            # 保存到数据库
            self.db.upsert_private_ai_config(
                user_name=user_name,
                enabled=updated_config.get('enabled', True),
                ai_agent_id=updated_config.get('ai_agent_id', 4),
                context_limit=updated_config.get('context_limit', 20),
                session_hours=updated_config.get('session_hours', 24),
                personality=updated_config.get('personality')
            )

            # 清除缓存
            if user_name in self.config_cache:
                del self.config_cache[user_name]
            if user_name in self.last_cache_update:
                del self.last_cache_update[user_name]

            self.handler._log(f"[私聊AI助手] 更新用户 {user_name} 配置成功")

        except Exception as e:
            self.handler._log(f"[私聊AI助手] 更新用户配置失败: {e}", level="ERROR")

    def disable_user(self, user_name: str):
        """禁用用户的AI助手功能"""
        self.update_user_config(user_name, enabled=False)

    def enable_user(self, user_name: str):
        """启用用户的AI助手功能"""
        self.update_user_config(user_name, enabled=True)

    def cleanup_old_data(self, days_to_keep: int = 90):
        """清理旧数据"""
        try:
            # 清理会话统计
            deleted_sessions = self.db.cleanup_old_sessions(days_to_keep)

            # 清理对话历史
            deleted_conversations = self.db.cleanup_old_conversations(days_to_keep)

            self.handler._log(f"[私聊AI助手] 清理完成: {deleted_sessions} 条会话统计, {deleted_conversations} 条对话记录")

            return deleted_sessions + deleted_conversations

        except Exception as e:
            self.handler._log(f"[私聊AI助手] 清理旧数据失败: {e}", level="ERROR")
            return 0
