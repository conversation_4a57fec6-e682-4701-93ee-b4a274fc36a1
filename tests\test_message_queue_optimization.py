"""
消息队列优化测试脚本
用于验证窗口切换时消息不丢失的优化效果
"""

import time
import threading
from collections import deque
from unittest.mock import Mock, MagicMock
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class MockWeChat:
    """模拟微信客户端"""
    
    def __init__(self):
        self.listen = {}
        self.message_buffer = deque()
        self.current_chat = None
        self.switch_delay = 0.1  # 模拟窗口切换延迟
        
    def GetListenMessage(self):
        """模拟获取监听消息"""
        if self.message_buffer:
            # 模拟在窗口切换时可能丢失消息
            if self.current_chat:
                time.sleep(0.05)  # 模拟处理延迟
            return {'test_chat': [self.message_buffer.popleft()]} if self.message_buffer else {}
        return {}
    
    def GetNextNewMessage(self):
        """模拟获取新消息"""
        if self.message_buffer:
            return {'test_chat': [self.message_buffer.popleft()]} if self.message_buffer else {}
        return {}
    
    def ChatWith(self, chat, exact=True):
        """模拟切换聊天窗口"""
        time.sleep(self.switch_delay)  # 模拟切换延迟
        self.current_chat = chat
        return True
    
    def AddListenChat(self, chat):
        """模拟添加监听"""
        self.listen[chat] = Mock()
        return True
    
    def RemoveListenChat(self, chat):
        """模拟移除监听"""
        self.listen.pop(chat, None)
        return True
    
    def CurrentChat(self, details=False):
        """模拟获取当前聊天信息"""
        return {'chat_type': 'group'}
    
    def add_test_message(self, content):
        """添加测试消息"""
        msg = Mock()
        msg.content = content
        msg.sender = 'test_user'
        msg.type = 'friend'
        self.message_buffer.append(msg)


class MessageQueueTester:
    """消息队列测试器"""
    
    def __init__(self):
        self.received_messages = []
        self.lost_messages = []
        self.processing_times = []
        
    def test_concurrent_messages(self, handler, num_messages=50, num_threads=5):
        """测试并发消息处理"""
        print(f"🧪 测试并发消息处理 ({num_messages} 条消息, {num_threads} 个线程)")
        
        def send_messages(thread_id, count):
            for i in range(count):
                message = f"Thread-{thread_id}-Message-{i}"
                handler.wx.add_test_message(message)
                time.sleep(0.01)  # 模拟消息间隔
        
        # 启动多个线程发送消息
        threads = []
        messages_per_thread = num_messages // num_threads
        
        start_time = time.time()
        
        for i in range(num_threads):
            thread = threading.Thread(
                target=send_messages, 
                args=(i, messages_per_thread)
            )
            threads.append(thread)
            thread.start()
        
        # 模拟消息处理
        processed_count = 0
        while processed_count < num_messages and time.time() - start_time < 30:
            # 模拟主循环
            handler._collect_all_messages()
            handler._process_message_queue()
            
            # 统计处理的消息
            current_buffer_size = sum(len(msgs) for msgs in handler.buffers.values())
            if current_buffer_size > processed_count:
                processed_count = current_buffer_size
            
            time.sleep(0.1)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 处理完成: {processed_count}/{num_messages} 条消息")
        print(f"⏱️  处理时间: {processing_time:.2f} 秒")
        print(f"📊 处理速度: {processed_count/processing_time:.2f} 消息/秒")
        
        return processed_count, processing_time
    
    def test_window_switching(self, handler, num_switches=20):
        """测试窗口切换时的消息完整性"""
        print(f"🧪 测试窗口切换消息完整性 ({num_switches} 次切换)")
        
        # 准备测试消息
        test_messages = [f"Switch-Test-{i}" for i in range(num_switches)]
        
        for i, message in enumerate(test_messages):
            # 添加消息
            handler.wx.add_test_message(message)
            
            # 模拟窗口切换操作
            if i % 3 == 0:  # 每3条消息切换一次窗口
                handler._mark_read(f"test_chat_{i}")
            
            # 收集和处理消息
            handler._collect_all_messages()
            handler._process_message_queue()
            
            time.sleep(0.05)
        
        # 最终处理剩余消息
        for _ in range(5):
            handler._collect_all_messages()
            handler._process_message_queue()
            time.sleep(0.1)
        
        # 统计结果
        total_processed = sum(len(msgs) for msgs in handler.buffers.values())
        print(f"✅ 消息处理完成: {total_processed}/{len(test_messages)} 条")
        
        return total_processed, len(test_messages)
    
    def test_queue_performance(self, handler, queue_sizes=[10, 50, 100, 500]):
        """测试不同队列大小的性能"""
        print("🧪 测试队列性能")
        
        results = {}
        
        for size in queue_sizes:
            print(f"  测试队列大小: {size}")
            
            # 清空队列
            with handler.queue_lock:
                handler.message_queue.clear()
            
            # 添加测试消息
            for i in range(size):
                handler.wx.add_test_message(f"Perf-Test-{i}")
            
            # 测试处理时间
            start_time = time.time()
            
            handler._collect_all_messages()
            handler._process_message_queue()
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            results[size] = processing_time
            print(f"    处理时间: {processing_time:.4f} 秒")
        
        return results


def run_optimization_tests():
    """运行优化测试"""
    print("🚀 开始消息队列优化测试")
    print("=" * 50)
    
    # 模拟导入优化后的处理器
    try:
        from auto_hander import WeChatMessageHandler
        
        # 创建测试实例
        handler = WeChatMessageHandler.__new__(WeChatMessageHandler)
        
        # 手动初始化必要的属性（避免真实的微信连接）
        handler.wx = MockWeChat()
        handler.buffers = {}
        handler.last_msg_time = {}
        handler.listened = {}
        handler.last_sender_time = {}
        handler.chat_type_cache = {}
        handler.message_queue = deque()
        handler.queue_lock = threading.Lock()
        handler.processing_lock = threading.Lock()
        handler.window_switching = False
        handler.missed_messages = {}
        
        # 模拟日志方法
        handler._log = lambda msg, level='INFO': print(f"[{level}] {msg}")
        handler._normalize_title = lambda x: str(x).strip()
        
        # 创建测试器
        tester = MessageQueueTester()
        
        # 运行测试
        print("\n1. 并发消息测试")
        print("-" * 30)
        processed, time_taken = tester.test_concurrent_messages(handler, 100, 5)
        
        print("\n2. 窗口切换测试")
        print("-" * 30)
        switch_processed, switch_total = tester.test_window_switching(handler, 30)
        
        print("\n3. 队列性能测试")
        print("-" * 30)
        perf_results = tester.test_queue_performance(handler)
        
        # 输出总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结")
        print("=" * 50)
        print(f"并发处理成功率: {processed/100*100:.1f}%")
        print(f"窗口切换消息完整性: {switch_processed/switch_total*100:.1f}%")
        print("队列性能:")
        for size, time_cost in perf_results.items():
            print(f"  {size} 条消息: {time_cost:.4f} 秒 ({size/time_cost:.0f} 消息/秒)")
        
        if processed >= 95 and switch_processed >= switch_total * 0.95:
            print("\n✅ 优化测试通过！消息队列优化效果良好。")
        else:
            print("\n❌ 优化测试未完全通过，需要进一步调优。")
            
    except ImportError as e:
        print(f"❌ 无法导入优化后的处理器: {e}")
        print("请确保已正确应用消息队列优化。")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    run_optimization_tests()
