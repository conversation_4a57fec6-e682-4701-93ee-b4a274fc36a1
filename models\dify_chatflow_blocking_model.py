import json
import requests
from typing import Dict, Any

from models import AIModel


class DifyChatFlowBlockingModel(AIModel):
    def generate(self, context: str, prompt_template: str = None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "inputs": {},
            "query": context,
            "response_mode": "blocking",
            # "conversation_id": "test123",
            "user": "terminal_user"
        }

        try:
            response = requests.post(self.url, headers=headers, json=payload, timeout=60)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 检查响应格式是否符合预期
                if response_data.get("event") == "message" and "answer" in response_data:
                    return response_data["answer"]
                else:
                    return "\nDify 接口返回格式异常，请检查API配置。"
            else:
                return f"\nDify 接口请求失败，状态码：{response.status_code}，请检查网络或API状态。"
                
        except requests.exceptions.Timeout:
            return "\nDify 接口请求超时，请稍后重试。"
        except requests.exceptions.RequestException as e:
            return f"\n网络请求错误：{e}"
        except json.JSONDecodeError:
            return "\nDify 接口返回数据格式错误，无法解析JSON。"
        except Exception as e:
            return f"\n发生未知错误：{e}"
