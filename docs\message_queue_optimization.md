# 微信机器人消息队列优化方案

## 问题描述

在原有的微信机器人实现中，当需要切换监听窗口时（例如发送回复、标记已读等操作），可能会导致其他窗口的消息丢失。这是因为：

1. **窗口切换阻塞**：切换到某个聊天窗口时，无法同时监听其他窗口
2. **消息获取时机**：`GetListenMessage()` 和 `GetNextNewMessage()` 在窗口切换期间可能遗漏消息
3. **单线程处理**：顺序处理消息时，处理一个窗口会阻塞其他窗口的消息接收

## 优化方案

### 1. 消息队列机制

#### 核心思想
- **先收集，后处理**：将所有消息先收集到队列中，然后统一处理
- **线程安全**：使用锁机制保护队列操作
- **防丢失**：在窗口切换前后都进行消息收集

#### 实现细节
```python
# 新增的数据结构
self.message_queue: deque = deque()  # 全局消息队列
self.queue_lock = Lock()  # 队列操作锁
self.processing_lock = Lock()  # 消息处理锁
self.window_switching = False  # 窗口切换状态标记
```

### 2. 安全窗口切换

#### 核心方法：`_safe_window_switch()`
```python
def _safe_window_switch(self, target_chat: str, operation_func, *args, **kwargs):
    """安全的窗口切换操作，确保不丢失消息"""
    try:
        # 标记开始窗口切换
        self.window_switching = True
        
        # 在切换前先收集一次消息
        self._collect_all_messages()
        
        # 执行窗口切换操作
        result = operation_func(*args, **kwargs)
        
        # 切换后再收集一次消息，防止遗漏
        self._collect_all_messages()
        
        return result
    finally:
        # 重置窗口切换状态
        self.window_switching = False
```

#### 应用场景
- `_mark_read()` - 标记消息已读
- `_try_add_listen()` - 添加监听
- `_cache_msgs()` - 获取聊天类型

### 3. 消息收集优化

#### 统一消息收集：`_collect_all_messages()`
```python
def _collect_all_messages(self):
    """收集所有消息到队列，避免窗口切换时丢失"""
    try:
        # 收集监听消息
        listen_messages = self.wx.GetListenMessage() or {}
        for chat_wnd, msgs in listen_messages.items():
            # 添加到队列...
        
        # 收集新消息
        new_messages = self.wx.GetNextNewMessage() or {}
        for raw_chat, msgs in new_messages.items():
            # 添加到队列...
    except Exception as e:
        self._log(f"收集消息时发生错误: {e}", 'ERROR')
```

#### 队列处理：`_process_message_queue()`
```python
def _process_message_queue(self):
    """处理消息队列中的消息"""
    while True:
        message_item = None
        with self.queue_lock:
            if not self.message_queue:
                break
            message_item = self.message_queue.popleft()
        
        if message_item:
            # 处理消息...
```

### 4. 主循环优化

#### 新的处理流程
```python
def run(self):
    while True:
        # 🌟 优化：先收集所有消息到队列
        self._collect_all_messages()
        
        # 🌟 优化：处理队列中的消息
        self._process_message_queue()
        
        # 处理缓冲区中的消息（静默检测）
        for chat in list(self.buffers.keys()):
            if now_ts - self.last_msg_time.get(chat, 0) >= self.QUIET_SEC:
                self._handle_buffer(chat)
        
        # 清理超时的监听...
        time.sleep(0.5)
```

## 性能优势

### 1. 消息零丢失
- **双重保护**：窗口切换前后都收集消息
- **队列缓冲**：所有消息先进队列，确保不丢失
- **状态标记**：通过 `window_switching` 标记避免冲突

### 2. 并发处理能力
- **线程安全**：使用锁机制保护共享资源
- **异步发送**：单条模式支持并行发送消息
- **队列解耦**：消息收集和处理分离

### 3. 错误恢复
- **异常隔离**：单个消息处理失败不影响其他消息
- **重试机制**：窗口切换失败时自动重试
- **状态重置**：确保异常后状态正确恢复

## 配置选项

### 消息队列配置
```python
# utils/message_queue_config.py
class MessageQueueConfig:
    MAX_QUEUE_SIZE = 1000  # 最大队列长度
    COLLECT_INTERVAL = 0.1  # 消息收集间隔（秒）
    WINDOW_SWITCH_DELAY = 0.2  # 窗口切换后的等待时间（秒）
    ENABLE_DEDUPLICATION = True  # 是否启用消息去重
    ENABLE_PERFORMANCE_LOG = True  # 是否启用性能日志
```

### 在 config.json 中配置
```json
{
  "message_queue": {
    "max_queue_size": 1000,
    "collect_interval": 0.1,
    "window_switch_delay": 0.2,
    "enable_deduplication": true,
    "enable_performance_log": true
  }
}
```

## 使用方法

### 1. 自动启用
优化已集成到现有代码中，无需额外配置即可使用。

### 2. 监控日志
```
[微信消息处理] [DEBUG] 处理来自 '技术群' 的 3 条消息 (来源: listen)
[微信消息处理] [DEBUG] 本轮处理了 5 个消息批次
[微信消息处理] [DEBUG] 安全窗口切换: 技术群 -> 标记已读
```

### 3. 性能调优
- 调整 `COLLECT_INTERVAL` 控制收集频率
- 调整 `MAX_QUEUE_SIZE` 控制队列大小
- 启用 `ENABLE_PERFORMANCE_LOG` 监控性能

## 兼容性

### 向后兼容
- 所有现有插件无需修改
- 现有配置文件继续有效
- API 接口保持不变

### 模式支持
- **批量模式**：完全支持，性能提升明显
- **单条模式**：完全支持，增加并行发送能力

## 测试验证

### 测试场景
1. **多窗口同时发消息**：验证无消息丢失
2. **频繁窗口切换**：验证切换期间消息完整性
3. **高并发消息**：验证队列处理能力
4. **异常恢复**：验证错误处理机制

### 预期效果
- ✅ 消息零丢失
- ✅ 响应速度提升
- ✅ 系统稳定性增强
- ✅ 错误恢复能力提升
