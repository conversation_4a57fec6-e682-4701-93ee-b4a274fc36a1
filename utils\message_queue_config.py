"""
消息队列配置模块
用于配置消息队列的行为参数，优化消息处理性能
"""

class MessageQueueConfig:
    """消息队列配置类"""
    
    # 队列大小限制
    MAX_QUEUE_SIZE = 1000  # 最大队列长度
    
    # 消息收集间隔
    COLLECT_INTERVAL = 0.1  # 消息收集间隔（秒）
    
    # 窗口切换保护
    WINDOW_SWITCH_DELAY = 0.2  # 窗口切换后的等待时间（秒）
    WINDOW_SWITCH_RETRIES = 3  # 窗口切换重试次数
    
    # 消息去重
    ENABLE_DEDUPLICATION = True  # 是否启用消息去重
    DEDUP_WINDOW = 5.0  # 去重时间窗口（秒）
    
    # 性能监控
    ENABLE_PERFORMANCE_LOG = True  # 是否启用性能日志
    PERFORMANCE_LOG_INTERVAL = 60  # 性能日志输出间隔（秒）
    
    # 错误处理
    MAX_ERROR_COUNT = 10  # 最大错误计数
    ERROR_RESET_INTERVAL = 300  # 错误计数重置间隔（秒）
    
    @classmethod
    def get_config(cls) -> dict:
        """获取配置字典"""
        return {
            'max_queue_size': cls.MAX_QUEUE_SIZE,
            'collect_interval': cls.COLLECT_INTERVAL,
            'window_switch_delay': cls.WINDOW_SWITCH_DELAY,
            'window_switch_retries': cls.WINDOW_SWITCH_RETRIES,
            'enable_deduplication': cls.ENABLE_DEDUPLICATION,
            'dedup_window': cls.DEDUP_WINDOW,
            'enable_performance_log': cls.ENABLE_PERFORMANCE_LOG,
            'performance_log_interval': cls.PERFORMANCE_LOG_INTERVAL,
            'max_error_count': cls.MAX_ERROR_COUNT,
            'error_reset_interval': cls.ERROR_RESET_INTERVAL
        }
    
    @classmethod
    def update_config(cls, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(cls, key.upper()):
                setattr(cls, key.upper(), value)
    
    @classmethod
    def load_from_file(cls, config_path: str):
        """从文件加载配置"""
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                cls.update_config(**config.get('message_queue', {}))
        except Exception as e:
            print(f"加载消息队列配置失败: {e}")
    
    @classmethod
    def save_to_file(cls, config_path: str):
        """保存配置到文件"""
        try:
            import json
            import os
            
            # 读取现有配置
            config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 更新消息队列配置
            config['message_queue'] = cls.get_config()
            
            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存消息队列配置失败: {e}")


# 默认配置实例
default_config = MessageQueueConfig()
