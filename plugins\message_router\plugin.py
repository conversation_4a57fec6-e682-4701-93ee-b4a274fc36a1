"""
消息路由插件
智能识别和分发不同类型的消息，确保私聊和群聊@消息都能得到正确处理
解决插件优先级冲突导致的消息处理遗漏问题
"""

from typing import List, Optional, Dict, Any
import re
import time

from core.plugin_base import Plugin


class MessageRouterPlugin(Plugin):
    """消息路由插件 - 智能分发不同类型的消息"""
    
    priority = 50  # 最高优先级，在所有其他插件之前执行
    
    def __init__(self, handler):
        super().__init__(handler)
        
        # 消息处理队列
        self.message_queue = []
        self.processing_lock = False
        
        # 插件引用缓存
        self.private_ai_plugin = None
        self.conversation_history_plugin = None
        self.other_plugins = []
        
        # 消息类型识别缓存
        self.message_type_cache = {}
        
        self.handler._log("✅ [消息路由] 插件初始化完成")
    
    def _get_plugin_instances(self):
        """获取其他插件实例"""
        if hasattr(self.handler, 'plugins'):
            for plugin in self.handler.plugins:
                plugin_name = plugin.__class__.__name__
                
                if plugin_name == 'PrivateAIAssistantPlugin':
                    self.private_ai_plugin = plugin
                elif plugin_name == 'ConversationHistoryPlugin':
                    self.conversation_history_plugin = plugin
                elif plugin != self:  # 排除自己
                    self.other_plugins.append(plugin)
    
    def _is_private_chat(self, chat: str) -> bool:
        """判断是否为私聊"""
        chat_type_cache = getattr(self.handler, 'chat_type_cache', {})
        chat_type = chat_type_cache.get(chat, 'friend')
        return chat_type == 'friend'
    
    def _is_at_message(self, messages: List[str]) -> bool:
        """判断是否包含@消息或AI相关的呼叫"""
        # 检测常见的@模式和AI呼叫模式
        at_patterns = [
            r'@.*?AI',
            r'@.*?助手',
            r'@.*?机器人',
            r'@.*?bot',
            r'@\S+\s*(AI|ai|助手|机器人)',
            r'@\S+.*?(AI|ai|助手|机器人)',
        ]

        # 检测直接呼叫AI的模式（不一定有@符号）
        ai_call_patterns = [
            r'^(AI|ai|助手|机器人)[，,：:\s]',  # 以AI开头
            r'(AI|ai|助手|机器人)[，,：:\s].*',  # 包含AI并有标点符号
            r'.*[，,。！!？?]\s*(AI|ai|助手|机器人)',  # 句子中呼叫AI
        ]

        for message in messages:
            # 检测@模式
            for pattern in at_patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    return True

            # 检测AI呼叫模式
            for pattern in ai_call_patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    return True

        return False
    
    def _contains_keywords(self, messages: List[str], keywords: List[str]) -> bool:
        """检查消息是否包含关键词"""
        for message in messages:
            for keyword in keywords:
                if keyword.lower() in message.lower():
                    return True
        return False
    
    def _analyze_message_type(self, chat: str, messages: List[str]) -> Dict[str, Any]:
        """分析消息类型和处理策略"""
        cache_key = f"{chat}_{hash(''.join(messages))}"
        
        if cache_key in self.message_type_cache:
            return self.message_type_cache[cache_key]
        
        analysis = {
            'is_private': self._is_private_chat(chat),
            'is_group': not self._is_private_chat(chat),
            'has_at': self._is_at_message(messages),
            'has_ai_keywords': self._contains_keywords(messages, ["AI", "ai", "助手", "机器人"]),
            'needs_private_ai': False,
            'needs_group_ai': False,
            'needs_other_plugins': True
        }
        
        # 决定处理策略
        if analysis['is_private']:
            # 私聊消息 - 由private_ai_assistant处理
            analysis['needs_private_ai'] = True
            analysis['needs_other_plugins'] = False  # 私聊不需要其他插件
        
        elif analysis['is_group']:
            # 群聊消息
            if analysis['has_at'] or analysis['has_ai_keywords']:
                # 包含@或AI关键词 - 由conversation_history处理
                analysis['needs_group_ai'] = True
            
            # 群聊消息总是需要其他插件处理（如关键词回复等）
            analysis['needs_other_plugins'] = True
        
        # 缓存分析结果
        self.message_type_cache[cache_key] = analysis
        
        return analysis
    
    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        消息路由处理 - 智能分发到对应的插件
        """
        if not messages:
            return None
        
        # 防止重复处理
        if self.processing_lock:
            return None
        
        try:
            self.processing_lock = True
            
            # 获取插件实例（延迟加载）
            if not self.private_ai_plugin or not self.conversation_history_plugin:
                self._get_plugin_instances()
            
            # 分析消息类型
            analysis = self._analyze_message_type(chat, messages)
            
            responses = []
            
            # 处理私聊消息
            if analysis['needs_private_ai'] and self.private_ai_plugin:
                try:
                    private_response = self.private_ai_plugin.on_messages(chat, messages)
                    if private_response:
                        responses.append(private_response)
                        self.handler._log(f"[消息路由] 私聊AI处理完成: {chat}")
                except Exception as e:
                    self.handler._log(f"[消息路由] 私聊AI处理失败: {e}", level="ERROR")
            
            # 处理群聊AI消息
            if analysis['needs_group_ai'] and self.conversation_history_plugin:
                try:
                    group_response = self.conversation_history_plugin.on_messages(chat, messages)
                    if group_response:
                        responses.append(group_response)
                        self.handler._log(f"[消息路由] 群聊AI处理完成: {chat}")
                except Exception as e:
                    self.handler._log(f"[消息路由] 群聊AI处理失败: {e}", level="ERROR")
            
            # 处理其他插件（如关键词回复等）
            if analysis['needs_other_plugins']:
                for plugin in self.other_plugins:
                    try:
                        # 跳过已经处理过的插件
                        if (plugin == self.private_ai_plugin or 
                            plugin == self.conversation_history_plugin):
                            continue
                        
                        if hasattr(plugin, 'on_messages'):
                            other_response = plugin.on_messages(chat, messages)
                            if other_response:
                                responses.append(other_response)
                                self.handler._log(f"[消息路由] {plugin.__class__.__name__} 处理完成: {chat}")
                    except Exception as e:
                        self.handler._log(f"[消息路由] {plugin.__class__.__name__} 处理失败: {e}", level="ERROR")
            
            # 合并回复
            if responses:
                # 如果有多个回复，用换行分隔
                combined_response = "\n\n".join(responses)
                
                self.handler._log(f"[消息路由] 消息处理完成，生成 {len(responses)} 个回复")
                return combined_response
            
            return None
            
        finally:
            self.processing_lock = False
    
    def _log_message_analysis(self, chat: str, messages: List[str], analysis: Dict[str, Any]):
        """记录消息分析结果（调试用）"""
        message_preview = messages[0][:30] + "..." if len(messages[0]) > 30 else messages[0]
        
        self.handler._log(f"[消息路由] 消息分析 - 聊天: {chat}")
        self.handler._log(f"[消息路由] 消息内容: {message_preview}")
        self.handler._log(f"[消息路由] 私聊: {analysis['is_private']}, 群聊: {analysis['is_group']}")
        self.handler._log(f"[消息路由] @消息: {analysis['has_at']}, AI关键词: {analysis['has_ai_keywords']}")
        self.handler._log(f"[消息路由] 处理策略 - 私聊AI: {analysis['needs_private_ai']}, 群聊AI: {analysis['needs_group_ai']}, 其他插件: {analysis['needs_other_plugins']}")
    
    def clear_cache(self):
        """清理缓存"""
        self.message_type_cache.clear()
        self.handler._log("[消息路由] 缓存已清理")
    
    def get_routing_stats(self) -> Dict[str, int]:
        """获取路由统计信息"""
        stats = {
            'cache_size': len(self.message_type_cache),
            'private_ai_available': self.private_ai_plugin is not None,
            'group_ai_available': self.conversation_history_plugin is not None,
            'other_plugins_count': len(self.other_plugins)
        }
        return stats
    
    def force_reload_plugins(self):
        """强制重新加载插件实例"""
        self.private_ai_plugin = None
        self.conversation_history_plugin = None
        self.other_plugins = []
        self._get_plugin_instances()
        self.handler._log("[消息路由] 插件实例已重新加载")
