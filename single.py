"""单条即时回复处理器

继承自批量版 WeChatMessageHandler，改写为 *收到一条立刻回复一条*。
配合 main.py 中的配置开关使用。
"""

from typing import Dict, List
from time import time, sleep
from threading import Lock
from concurrent.futures import ThreadPoolExecutor

from auto_hander import WeChatMessageHandler


class SingleMessageHandler(WeChatMessageHandler):
    """逐条回复模式：不做静默聚合，收到即回复，但带 **去重 & 频率限制**"""

    QUIET_SEC = 0  # 不使用静默阈值（立刻处理）
    MIN_INTERVAL_SEC = 0.5  # 🌟 两条回复最小间隔，防止消息连在一起

    def __init__(self):
        super().__init__()
        # 最近一次回复记录 <chat, (timestamp, content)>
        self._last_reply: Dict[str, tuple] = {}
        self._reply_lock = Lock()  # 保护 _last_reply
        # 🌟 新增：线程池执行器，用于并行发送消息
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="SingleReply")

    # ---------- 覆盖缓存逻辑 ---------- #

    def _cache_msgs(self, chat: str, msgs):
        """直接逐条处理，不写入 buffers"""
        chat = self._normalize_title(chat)

        # 🚫 首先进行黑名单检查 - 最高优先级
        for plugin in self.plugins:
            if plugin.__class__.__name__ == 'BlacklistPlugin':
                try:
                    contents_for_check = [getattr(m, 'content', '') for m in msgs if getattr(m, 'type', '') == 'friend']
                    if contents_for_check:
                        reply = plugin.on_messages(chat, contents_for_check)
                        if reply == "__BLACKLISTED__":
                            self._log(f"[系统] 消息来自黑名单聊天 '{chat}'，跳过", 'DEBUG')
                            return
                except Exception as e:
                    self._log(f"[黑名单插件异常] {e}", 'ERROR')
                break

        contents: List[str] = [getattr(m, 'content', '') for m in msgs if getattr(m, 'type', '') == 'friend']
        if not contents:
            return

        for content in contents:
            self._handle_single(chat, content)

    # ---------- 单条消息处理 ---------- #

    def _handle_single(self, chat: str, content: str):
        self._log(f"收到来自 '{chat}' 的消息: {content}")

        reply = None
        for plugin in self.plugins:
            reply = plugin.on_messages(chat, [content])  # 仍复用原插件接口，传入单条 list
            if reply == "__BLACKLISTED__":
                self._log(f"[系统] 消息来自黑名单聊天 '{chat}'，终止处理流程")
                return
            if reply is not None:
                break

        if not reply:
            self._log("无插件响应本次消息")
            self._try_add_listen(chat)
            return

        # ---------- 去重 + 频率限制 ---------- #
        now = time()
        with self._reply_lock:
            last_ts, last_text = self._last_reply.get(chat, (0.0, ""))
            if reply == last_text and (now - last_ts) < self.MIN_INTERVAL_SEC:
                self._log(
                    f"跳过重复回复(间隔 < {self.MIN_INTERVAL_SEC}s): {reply}",
                    "DEBUG",
                )
                return  # 太快或内容重复，直接忽略
            # 若需等待以满足最小间隔
            if (now - last_ts) < self.MIN_INTERVAL_SEC:
                wait = self.MIN_INTERVAL_SEC - (now - last_ts)
                self._log(f"控制发送频率，睡眠 {wait:.2f}s", "DEBUG")
                sleep(wait)
            # 更新记录
            self._last_reply[chat] = (time(), reply)

        # 使用父类并行发送逻辑
        self.executor.submit(self._send_and_fetch_missed, chat, reply)

        # 依旧保持监听
        self._try_add_listen(chat)

    # ---------- 禁用批量缓冲处理 ---------- #

    def _handle_buffer(self, chat: str):
        """子类中不需要批量缓冲，留空即可"""
        return

    def _send_and_fetch_missed(self, chat: str, reply: str):
        """发送消息并获取可能错过的消息"""
        try:
            # 获取聊天类型
            chat_type = self.chat_type_cache.get(chat, 'friend')
            is_group = chat_type == 'group'

            # 发送前先收集消息
            self._collect_all_messages()

            # 发送消息
            if is_group:
                from group_reply import send_group_reply
                send_group_reply(self.wx, chat, reply, self._log)
            else:
                from private_reply import send_private_reply
                send_private_reply(self.wx, chat, reply, self._log)

            # 发送后再收集消息，防止遗漏
            self._collect_all_messages()

        except Exception as e:
            self._log(f"发送消息时发生错误: {e}", 'ERROR')