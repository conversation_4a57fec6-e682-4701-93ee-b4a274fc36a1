# 消息路由插件

## 概述

消息路由插件是一个智能消息分发系统，解决了私聊和群聊@消息同时处理的问题。它能够准确识别不同类型的消息，并将其路由到对应的处理插件，确保所有消息都能得到正确处理。

## 核心问题解决

### 🔧 原有问题
1. **插件优先级冲突**：private_ai_assistant(100) → conversation_history(200)
2. **消息处理独占**：一个插件返回回复后，后续插件不再执行
3. **消息遗漏**：同时有私聊和群聊@消息时，只有私聊被处理

### ✅ 解决方案
1. **智能消息路由**：最高优先级(50)，统一分发消息
2. **并行处理机制**：不同类型消息可以同时处理
3. **精确消息识别**：准确区分私聊、群聊、@消息、AI呼叫

## 核心功能

### 🎯 智能消息识别

#### 私聊检测
```python
def _is_private_chat(self, chat: str) -> bool:
    chat_type_cache = getattr(self.handler, 'chat_type_cache', {})
    chat_type = chat_type_cache.get(chat, 'friend')
    return chat_type == 'friend'
```

#### @消息和AI呼叫检测
```python
# 支持的模式
at_patterns = [
    r'@.*?AI',           # @AI
    r'@.*?助手',         # @助手
    r'@.*?机器人',       # @机器人
    r'@\S+.*?(AI|ai|助手|机器人)',  # @用户 AI
]

ai_call_patterns = [
    r'^(AI|ai|助手|机器人)[，,：:\s]',      # AI开头
    r'(AI|ai|助手|机器人)[，,：:\s].*',     # AI + 标点
    r'.*[，,。！!？?]\s*(AI|ai|助手|机器人)', # 句中呼叫
]
```

### 🚀 智能路由策略

#### 消息分析
```python
def _analyze_message_type(self, chat: str, messages: List[str]) -> Dict:
    analysis = {
        'is_private': self._is_private_chat(chat),
        'is_group': not self._is_private_chat(chat),
        'has_at': self._is_at_message(messages),
        'has_ai_keywords': self._contains_keywords(messages, ["AI", "ai", "助手", "机器人"]),
        'needs_private_ai': False,
        'needs_group_ai': False,
        'needs_other_plugins': True
    }
    
    # 决定处理策略
    if analysis['is_private']:
        analysis['needs_private_ai'] = True
        analysis['needs_other_plugins'] = False
    elif analysis['is_group']:
        if analysis['has_at'] or analysis['has_ai_keywords']:
            analysis['needs_group_ai'] = True
        analysis['needs_other_plugins'] = True
    
    return analysis
```

#### 路由决策表
| 消息类型 | 私聊AI | 群聊AI | 其他插件 | 说明 |
|---------|--------|--------|----------|------|
| 私聊消息 | ✅ | ❌ | ❌ | 只由私聊AI处理 |
| 群聊@AI | ❌ | ✅ | ✅ | 群聊AI + 其他插件 |
| 群聊AI关键词 | ❌ | ✅ | ✅ | 群聊AI + 其他插件 |
| 群聊普通消息 | ❌ | ❌ | ✅ | 只由其他插件处理 |

### 📊 并行处理机制

#### 处理流程
```python
def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
    # 1. 分析消息类型
    analysis = self._analyze_message_type(chat, messages)
    
    responses = []
    
    # 2. 处理私聊消息
    if analysis['needs_private_ai']:
        private_response = self.private_ai_plugin.on_messages(chat, messages)
        if private_response:
            responses.append(private_response)
    
    # 3. 处理群聊AI消息
    if analysis['needs_group_ai']:
        group_response = self.conversation_history_plugin.on_messages(chat, messages)
        if group_response:
            responses.append(group_response)
    
    # 4. 处理其他插件
    if analysis['needs_other_plugins']:
        for plugin in self.other_plugins:
            other_response = plugin.on_messages(chat, messages)
            if other_response:
                responses.append(other_response)
    
    # 5. 合并回复
    if responses:
        return "\n\n".join(responses)
    
    return None
```

## 插件架构

### 优先级设计
```
50  - MessageRouterPlugin      (消息路由，最高优先级)
150 - PrivateAIAssistantPlugin (私聊AI，由路由调用)
250 - ConversationHistoryPlugin (群聊AI，由路由调用)
300+ - 其他插件                (关键词回复等，由路由调用)
```

### 插件协作
```
MessageRouterPlugin (路由中心)
├── PrivateAIAssistantPlugin (私聊处理)
├── ConversationHistoryPlugin (群聊AI处理)
└── OtherPlugins (其他功能插件)
```

## 使用示例

### 基本使用场景

#### 场景1：纯私聊
```
用户私聊: "你好"
路由分析: 私聊消息
处理结果: 私聊AI回复
```

#### 场景2：群聊@AI
```
群聊消息: "@AI 你好"
路由分析: 群聊@消息
处理结果: 群聊AI回复 + 其他插件回复
```

#### 场景3：群聊AI关键词
```
群聊消息: "AI，今天天气怎么样"
路由分析: 群聊AI关键词
处理结果: 群聊AI回复 + 其他插件回复
```

#### 场景4：群聊普通消息
```
群聊消息: "大家好"
路由分析: 群聊普通消息
处理结果: 其他插件回复（如关键词回复）
```

### 并发处理场景

#### 场景：同时收到私聊和群聊@消息
```
时间点1: 用户A私聊 "你好"
时间点2: 群聊B "@AI 帮忙"

处理结果:
- 用户A收到: 私聊AI回复
- 群聊B收到: 群聊AI回复 + 其他插件回复
```

## 配置和部署

### 自动发现
插件会自动发现和加载，无需手动配置：
```
plugins/
├── message_router/
│   ├── __init__.py
│   ├── plugin.py
│   └── ...
```

### 插件引用
消息路由插件会自动获取其他插件的引用：
```python
def _get_plugin_instances(self):
    if hasattr(self.handler, 'plugins'):
        for plugin in self.handler.plugins:
            plugin_name = plugin.__class__.__name__
            
            if plugin_name == 'PrivateAIAssistantPlugin':
                self.private_ai_plugin = plugin
            elif plugin_name == 'ConversationHistoryPlugin':
                self.conversation_history_plugin = plugin
            elif plugin != self:
                self.other_plugins.append(plugin)
```

### 性能优化
- **消息类型缓存**：避免重复分析相同消息
- **插件引用缓存**：避免重复查找插件实例
- **处理锁机制**：防止重复处理同一消息

## 监控和调试

### 日志输出
```
✅ 正常运行:
[消息路由] 插件初始化完成
[消息路由] 私聊AI处理完成: 用户A
[消息路由] 群聊AI处理完成: 技术群
[消息路由] KeywordPlugin 处理完成: 技术群
[消息路由] 消息处理完成，生成 2 个回复

❌ 错误处理:
[ERROR] [消息路由] 私聊AI处理失败: API调用超时
[ERROR] [消息路由] 群聊AI处理失败: 数据库连接错误
```

### 统计信息
```python
# 获取路由统计
stats = router_plugin.get_routing_stats()
# 输出: {
#     'cache_size': 50,
#     'private_ai_available': True,
#     'group_ai_available': True,
#     'other_plugins_count': 3
# }
```

### 调试功能
```python
# 清理缓存
router_plugin.clear_cache()

# 强制重新加载插件
router_plugin.force_reload_plugins()

# 查看消息分析结果（调试模式）
router_plugin._log_message_analysis(chat, messages, analysis)
```

## 测试验证

### 运行测试
```bash
python plugins/message_router/test_message_routing.py
```

### 测试覆盖
- ✅ 私聊检测功能
- ✅ @消息和AI呼叫检测
- ✅ 消息分析功能
- ✅ 消息路由功能
- ✅ 并发消息处理

## 扩展功能

### 自定义路由规则
```python
# 可以扩展消息分析逻辑
def _analyze_message_type(self, chat: str, messages: List[str]) -> Dict:
    # 添加自定义分析规则
    analysis = super()._analyze_message_type(chat, messages)
    
    # 自定义逻辑
    if self._is_vip_user(chat):
        analysis['needs_priority_handling'] = True
    
    return analysis
```

### 插件热加载
```python
# 支持动态添加新插件
def register_plugin(self, plugin):
    if plugin not in self.other_plugins:
        self.other_plugins.append(plugin)
```

## 注意事项

1. **插件顺序**：确保消息路由插件优先级最高(50)
2. **插件依赖**：其他插件应该调整优先级，避免直接处理消息
3. **错误处理**：单个插件失败不会影响其他插件处理
4. **性能考虑**：大量消息时注意缓存清理
5. **兼容性**：与现有插件完全兼容，无需修改现有逻辑

消息路由插件完美解决了私聊和群聊@消息的并发处理问题，确保所有类型的消息都能得到正确、及时的处理。
