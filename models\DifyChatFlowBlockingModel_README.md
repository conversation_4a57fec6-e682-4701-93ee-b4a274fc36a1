# Dify ChatFlow Blocking Model

## 概述

`DifyChatFlowBlockingModel` 是基于现有 `DifyChatFlowModel` 创建的新模型，专门用于处理堵塞模式（blocking mode）的 Dify API 请求。

## 主要区别

### 1. 请求模式
- **原模型 (DifyChatFlowModel)**: `response_mode: "streaming"`
- **新模型 (DifyChatFlowBlockingModel)**: `response_mode: "blocking"`

### 2. 响应处理
- **原模型**: 处理流式响应，逐块读取数据
- **新模型**: 处理单次完整响应，直接解析 JSON

### 3. 响应格式
新模型期望的响应格式：
```json
{
    "event": "message",
    "message_id": "9da23599-e713-473b-982c-4328d4f5c78a",
    "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2",
    "mode": "chat",
    "answer": "iPhone 13 Pro Max specs are listed here:...",
    "metadata": {
        "usage": {
            "prompt_tokens": 1033,
            "completion_tokens": 128,
            "total_tokens": 1161,
            "total_price": "0.0012890",
            "currency": "USD",
            "latency": 0.7682376249867957
        },
        "retriever_resources": [...]
    },
    "created_at": 1705407629
}
```

## 使用方法

### 1. 数据库配置
首先运行 SQL 脚本添加新模型类型：
```sql
-- 运行 models/add_dify_chatflow_blocking_model.sql
```

### 2. 创建智能体配置
```sql
INSERT INTO ai_agent_profiles (
    name, 
    description, 
    api_key, 
    url, 
    model_type
) VALUES (
    'Dify ChatFlow Blocking Agent',
    'Dify ChatFlow 堵塞模式智能体',
    'your_dify_api_key',
    'https://api.dify.ai/v1/chat-messages',
    'dify_chatflow_blocking'
);
```

### 3. 在插件中使用
模型已自动注册到以下插件的 MODEL_MAP 中：
- `AIChatPlugin`
- `PrivateAIAssistantPlugin` 
- `ConversationHistoryPlugin`

## 性能优势

### 1. 更快的响应速度
- 堵塞模式避免了流式处理的开销
- 减少了网络往返次数
- 更适合短对话场景

### 2. 更简单的错误处理
- 单次请求，更容易处理超时和错误
- 避免了流式处理中的连接中断问题

### 3. 更低的资源消耗
- 不需要维护长连接
- 减少了内存使用

## 适用场景

### 推荐使用堵塞模式的情况：
- 短文本回复（< 500 字符）
- 对响应速度要求高的场景
- 网络环境不稳定的情况
- 简单问答场景

### 推荐使用流式模式的情况：
- 长文本生成（> 500 字符）
- 需要实时显示生成过程
- 复杂推理任务
- 用户体验要求打字机效果

## 配置示例

### 智能体4配置为堵塞模式
```sql
UPDATE ai_agent_profiles SET 
    model_type = 'dify_chatflow_blocking'
WHERE id = 4;
```

### 对话配置使用堵塞模式智能体
```sql
UPDATE conversation_config SET 
    ai_agent_id = (SELECT id FROM ai_agent_profiles WHERE model_type = 'dify_chatflow_blocking' LIMIT 1)
WHERE chat_name = '测试群聊';
```

## 错误处理

新模型包含完善的错误处理机制：
- 网络超时处理
- JSON 解析错误处理
- API 状态码错误处理
- 响应格式验证

## 注意事项

1. **API 兼容性**: 确保你的 Dify API 支持 `response_mode: "blocking"`
2. **超时设置**: 默认超时时间为 60 秒，可根据需要调整
3. **响应格式**: 模型期望特定的 JSON 响应格式，请确保 API 返回格式正确
4. **性能监控**: 建议监控响应时间，选择最适合的模式
