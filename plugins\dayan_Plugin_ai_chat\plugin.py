import os
import json
from typing import List, Optional, Dict, Any

# 抽象基类 Plugin 和 MySQLDB 数据库操作类
from core.plugin_base import Plugin
from db_plugins.mysql import MySQLDB  # 假设你有一个 MySQLDB 类来处理数据库操作
# 模型基类和具体模型实现
from models import (
    AIModel,
    GPT35TurboModel,
    DifyChatFlowModel,
    DifyChatFlowBlockingModel,
    DifyWorkFlowModel,
    DifyAgentModel
)
# 导入插件的SQL函数
from .sql import AI_CHAT_SQL_FUNCTIONS
# 导入对话历史记录功能
from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS


class AIChatPlugin(Plugin):
    MODEL_MAP = {
        "gpt-3.5-turbo": GPT35TurboModel,
        "dify_chatflow": DifyChatFlowModel,
        "dify_chatflow_blocking": DifyChatFlowBlockingModel,
        "dify_workflow": DifyWorkFlowModel,
        "dify_agent": DifyAgentModel
    }

    priority = 150

    def __init__(self, handler):
        super().__init__(handler)

        # 加载数据库配置文件路径
        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("AIChatPlugin", AI_CHAT_SQL_FUNCTIONS)

        # 注册对话历史记录功能
        self.db.register_plugin_functions("AIChatPlugin_History", CONVERSATION_HISTORY_SQL_FUNCTIONS)

        # 初始化对话历史记录表
        try:
            self.db.init_conversation_tables()
            self.handler._log("✅ [AIChatPlugin] 对话记录表初始化完成")
        except Exception as e:
            self.handler._log(f"❌ [AIChatPlugin] 初始化对话记录表失败: {e}", level="ERROR")

        # 缓存规则数据
        self.cache = self._load_all_rules_from_db()
        self.context_cache = {}  # 可用于缓存多轮对话

        # 存储AI回复的标识，用于避免重复存储
        self.ai_reply_cache = set()

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("AIChatPlugin")
            self.db.unregister_plugin_functions("AIChatPlugin_History")

    def _load_all_rules_from_db(self) -> Dict[str, Any]:
        results = self.db.get_all_ai_keyword_rules_with_agent()

        cache = {
            "default_group_keywords": [],
            "default_private_keywords": [],
            "chat_specific_keywords": {}
        }

        chat_data = {}

        for row in results:
            chat_name = row["chat_name"]
            chat_type = row["chat_type"]
            global_rule = row["global_rule"]
            keyword = row["user_keyword"]
            agent_id = row["agent_id"]
            reply_prompt = row["reply_prompt"]

            if not keyword or not agent_id:
                continue

            if chat_name not in chat_data:
                chat_data[chat_name] = {
                    "type": chat_type,
                    "keywords": []
                }

            chat_data[chat_name]["keywords"].append((keyword, agent_id, reply_prompt))

        for chat_name, data in chat_data.items():
            if data.pop("global_rule", False):
                if data["type"] == "group":
                    cache["default_group_keywords"].extend(data["keywords"])
                else:
                    cache["default_private_keywords"].extend(data["keywords"])
            else:
                cache["chat_specific_keywords"][chat_name] = data

        return cache

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        chat_config = self.cache["chat_specific_keywords"].get(chat, {})
        chat_type = chat_config.get("type", "group")
        chat_enabled = chat_config.get("enabled", True)

        if not chat_enabled:
            return None

        matched_keyword = None
        matched_agent_id = None
        matched_prompt = None

        local_keywords = chat_config.get("keywords", [])
        # 匹配局部关键词规则
        for msg in reversed(messages):
            for keyword, agent_id, reply_prompt in local_keywords:
                if keyword in msg:
                    matched_keyword = keyword
                    matched_agent_id = agent_id
                    matched_prompt = reply_prompt
                    break
            if matched_keyword:
                break

        if not matched_keyword:
            # 尝试全局关键词
            keywords_list = self.cache[
                "default_group_keywords" if chat_type == "group" else "default_private_keywords"
            ]
            for msg in reversed(messages):
                for keyword, agent_id, reply_prompt in keywords_list:
                    if keyword in msg:
                        matched_keyword = keyword
                        matched_agent_id = agent_id
                        matched_prompt = reply_prompt
                        break
                if matched_keyword:
                    break

        if matched_keyword and matched_agent_id:
            # 先保存用户消息到历史记录
            user_message_info = self._save_user_messages_to_history(chat, chat_type, messages)

            agent_config = self._get_agent_config_by_id(matched_agent_id)
            if agent_config:
                model_type = agent_config.get("model_type", "gpt-3.5-turbo")
                model_class = self.MODEL_MAP.get(model_type)

                if not model_class:
                    self.handler._log(f"[AIChatPlugin] 不支持的模型类型: {model_type}")
                    return "抱歉，不支持该模型类型。"

                # 实例化模型
                model_kwargs = {
                    "api_key": agent_config["api_key"],
                    "url": agent_config["url"]
                }

                ai_model: AIModel = model_class(**model_kwargs)
                ai_response = ai_model.generate(
                    context=self._build_context(messages),
                    prompt_template=matched_prompt
                )

                if ai_response:
                    # 保存AI回复到历史记录，建立关联关系
                    self._save_ai_reply_to_history(chat, chat_type, ai_response, user_message_info, matched_agent_id)

                    self.handler._log(f"[AIChatPlugin] 【{chat_type}】已生成并存储 AI 回复：{ai_response[:30]}...")
                    return ai_response

        return None

    def _get_agent_config_by_id(self, agent_id: int) -> Optional[Dict]:
        return self.db.get_ai_agent_by_id(agent_id)

    def _build_context(self, messages: List[str]) -> str:
        return "\n".join([f"用户：{msg}" for msg in messages])

    def _save_user_messages_to_history(self, chat: str, chat_type: str, messages: List[str]) -> Dict:
        """
        保存用户消息到历史记录

        Args:
            chat: 聊天窗口名称
            chat_type: 聊天类型 (group/private)
            messages: 用户消息列表

        Returns:
            Dict: 包含消息ID和对话ID的信息，用于后续AI回复关联
        """
        try:
            # 生成对话ID和批次ID
            conversation_id = self.db.generate_conversation_id()
            message_batch_id = self.db.generate_batch_id() if len(messages) > 1 else None

            # 合并所有用户消息
            combined_content = " ".join(messages)

            # 保存用户消息
            user_message_id = self.db.save_user_message_with_batch(
                chat_name=chat,
                chat_type=chat_type,
                sender="用户",  # 由于接口限制，暂时使用通用标识
                message_content=combined_content,
                session_hours=24,
                conversation_id=conversation_id,
                message_batch_id=message_batch_id,
                is_batch_start=len(messages) > 1,
                is_batch_end=len(messages) > 1
            )

            self.handler._log(f"[AIChatPlugin] 保存用户消息到历史记录 (ID: {user_message_id})")

            return {
                'user_message_id': user_message_id,
                'conversation_id': conversation_id,
                'message_batch_id': message_batch_id,
                'combined_content': combined_content
            }

        except Exception as e:
            self.handler._log(f"[AIChatPlugin] 保存用户消息失败: {e}", level="ERROR")
            return {}

    def _save_ai_reply_to_history(self, chat: str, chat_type: str, ai_response: str,
                                 user_message_info: Dict, agent_id: int):
        """
        保存AI回复到历史记录，建立与用户消息的关联

        Args:
            chat: 聊天窗口名称
            chat_type: 聊天类型
            ai_response: AI回复内容
            user_message_info: 用户消息信息
            agent_id: AI智能体ID
        """
        if not user_message_info:
            self.handler._log("[AIChatPlugin] 用户消息信息为空，无法保存AI回复关联", level="WARNING")
            return

        try:
            # 生成回复缓存键，避免重复存储
            cache_key = f"{chat}_{user_message_info.get('user_message_id')}_{hash(ai_response)}"
            if cache_key in self.ai_reply_cache:
                self.handler._log("[AIChatPlugin] AI回复已存储，跳过重复保存")
                return

            # 保存AI回复并建立关联
            reply_id = self.db.save_bot_reply(
                chat_name=chat,
                chat_type=chat_type,
                message_content=ai_response,
                session_hours=24,
                conversation_id=user_message_info.get('conversation_id'),
                reply_to_id=user_message_info.get('user_message_id'),
                message_batch_id=user_message_info.get('message_batch_id')
            )

            # 添加到缓存，避免重复存储
            self.ai_reply_cache.add(cache_key)

            self.handler._log(f"[AIChatPlugin] 保存AI回复到历史记录 (回复ID: {reply_id}, 关联消息ID: {user_message_info.get('user_message_id')}, 智能体ID: {agent_id})")

        except Exception as e:
            self.handler._log(f"[AIChatPlugin] 保存AI回复失败: {e}", level="ERROR")

    def get_conversation_history(self, chat: str, chat_type: str, limit: int = 20) -> List[Dict]:
        """
        获取聊天的对话历史记录

        Args:
            chat: 聊天窗口名称
            chat_type: 聊天类型
            limit: 返回记录数量限制

        Returns:
            List[Dict]: 对话历史记录列表
        """
        try:
            return self.db.get_conversation_context(chat, chat_type, limit)
        except Exception as e:
            self.handler._log(f"[AIChatPlugin] 获取对话历史失败: {e}", level="ERROR")
            return []

    def get_conversation_by_id(self, conversation_id: str) -> List[Dict]:
        """
        根据对话ID获取完整对话

        Args:
            conversation_id: 对话ID

        Returns:
            List[Dict]: 完整对话记录
        """
        try:
            return self.db.get_conversation_by_id(conversation_id)
        except Exception as e:
            self.handler._log(f"[AIChatPlugin] 根据ID获取对话失败: {e}", level="ERROR")
            return []

    def cleanup_old_conversations(self, days_to_keep: int = 30) -> int:
        """
        清理旧的对话记录

        Args:
            days_to_keep: 保留天数

        Returns:
            int: 删除的记录数量
        """
        try:
            deleted_count = self.db.cleanup_old_conversations(days_to_keep)
            self.handler._log(f"[AIChatPlugin] 清理了 {deleted_count} 条旧对话记录")
            return deleted_count
        except Exception as e:
            self.handler._log(f"[AIChatPlugin] 清理旧对话记录失败: {e}", level="ERROR")
            return 0